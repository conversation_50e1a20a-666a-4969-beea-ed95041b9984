// Copyright © 2025 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.

#pragma once

#include "CoreMinimal.h"
#include "Quantomb/Weapon/WeaponComponent/WeaponComponent.h"
#include "WeaponComponentSight.generated.h"

UCLASS()
class QUANTOMB_API AWeaponComponentSight : public AWeaponComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AWeaponComponentSight();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;


};
