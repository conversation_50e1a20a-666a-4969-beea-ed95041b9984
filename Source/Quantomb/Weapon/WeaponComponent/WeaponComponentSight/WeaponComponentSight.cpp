// Copyright © 2025 Super State Studio. All Rights Reserved. This file is part of Quantomb, developed by Super State Studio. Redistribution or modification of this code is not permitted without prior written consent from Super State Studio.


#include "WeaponComponentSight.h"


// Sets default values
AWeaponComponentSight::AWeaponComponentSight()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AWeaponComponentSight::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AWeaponComponentSight::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

